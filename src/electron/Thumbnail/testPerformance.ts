#!/usr/bin/env node

/**
 * Performance test script for thumbnail generation
 * Run this script to compare original vs optimized thumbnail generation
 */

import { generateThumbnail } from './generateThumbnail.js';
import { fastThumbnailGenerator } from './fastThumbnailGenerator.js';
import { performanceTester } from './performanceTest.js';
import { imageWorkerPool } from '../workers/workerPool.js';
import fs from 'fs/promises';
import path from 'path';

// Sample test images (you can replace these with actual image paths)
const TEST_IMAGES = [
  // Add your test image paths here
  // '/path/to/test/image1.jpg',
  // '/path/to/test/image2.png',
  // '/path/to/test/image3.webp',
];

async function createTestImages(): Promise<string[]> {
  // This function would create or find test images
  // For now, return empty array - user needs to provide test images
  console.log('Please add test image paths to TEST_IMAGES array in testPerformance.ts');
  return [];
}

async function runBasicPerformanceTest() {
  console.log('🚀 Starting Basic Performance Test\n');

  const testFiles = TEST_IMAGES.length > 0 ? TEST_IMAGES : await createTestImages();
  
  if (testFiles.length === 0) {
    console.log('❌ No test files available. Please add image paths to TEST_IMAGES array.');
    return;
  }

  console.log(`📁 Testing with ${testFiles.length} files:`);
  testFiles.forEach((file, i) => console.log(`   ${i + 1}. ${path.basename(file)}`));
  console.log();

  // Test 1: Original method
  console.log('🔄 Testing Original Method...');
  const originalStart = Date.now();
  const originalResults = [];
  
  for (const filePath of testFiles) {
    try {
      const result = await generateThumbnail(filePath);
      originalResults.push({ success: true, path: result.path });
    } catch (error: any) {
      originalResults.push({ success: false, error: error.message });
    }
  }
  
  const originalTime = Date.now() - originalStart;
  const originalSuccess = originalResults.filter(r => r.success).length;

  // Test 2: Optimized method (first run - no cache)
  console.log('⚡ Testing Optimized Method (no cache)...');
  fastThumbnailGenerator.clearCache();
  
  const optimizedStart = Date.now();
  const optimizedResults = [];
  
  for (const filePath of testFiles) {
    try {
      const result = await fastThumbnailGenerator.generateThumbnail(filePath);
      optimizedResults.push({ success: true, path: result.path });
    } catch (error: any) {
      optimizedResults.push({ success: false, error: error.message });
    }
  }
  
  const optimizedTime = Date.now() - optimizedStart;
  const optimizedSuccess = optimizedResults.filter(r => r.success).length;

  // Test 3: Optimized method (second run - with cache)
  console.log('💨 Testing Optimized Method (with cache)...');
  
  const cachedStart = Date.now();
  const cachedResults = [];
  
  for (const filePath of testFiles) {
    try {
      const result = await fastThumbnailGenerator.generateThumbnail(filePath);
      cachedResults.push({ success: true, path: result.path });
    } catch (error: any) {
      cachedResults.push({ success: false, error: error.message });
    }
  }
  
  const cachedTime = Date.now() - cachedStart;
  const cachedSuccess = cachedResults.filter(r => r.success).length;

  // Test 4: Batch generation
  console.log('📦 Testing Batch Generation...');
  fastThumbnailGenerator.clearCache();
  
  const batchStart = Date.now();
  const batchResults = await fastThumbnailGenerator.generateBatch(testFiles);
  const batchTime = Date.now() - batchStart;
  const batchSuccess = batchResults.filter(r => r.thumbnail !== null).length;

  // Print results
  console.log('\n' + '='.repeat(60));
  console.log('📊 PERFORMANCE RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n🐌 Original Method:`);
  console.log(`   Time: ${originalTime}ms`);
  console.log(`   Average: ${(originalTime / testFiles.length).toFixed(2)}ms per image`);
  console.log(`   Success: ${originalSuccess}/${testFiles.length}`);
  
  console.log(`\n⚡ Optimized Method (no cache):`);
  console.log(`   Time: ${optimizedTime}ms`);
  console.log(`   Average: ${(optimizedTime / testFiles.length).toFixed(2)}ms per image`);
  console.log(`   Success: ${optimizedSuccess}/${testFiles.length}`);
  console.log(`   Speedup: ${(originalTime / optimizedTime).toFixed(2)}x`);
  
  console.log(`\n💨 Optimized Method (cached):`);
  console.log(`   Time: ${cachedTime}ms`);
  console.log(`   Average: ${(cachedTime / testFiles.length).toFixed(2)}ms per image`);
  console.log(`   Success: ${cachedSuccess}/${testFiles.length}`);
  console.log(`   Speedup: ${(originalTime / cachedTime).toFixed(2)}x`);
  
  console.log(`\n📦 Batch Generation:`);
  console.log(`   Time: ${batchTime}ms`);
  console.log(`   Average: ${(batchTime / testFiles.length).toFixed(2)}ms per image`);
  console.log(`   Success: ${batchSuccess}/${testFiles.length}`);
  console.log(`   Speedup: ${(originalTime / batchTime).toFixed(2)}x`);

  // Worker stats
  const workerStats = imageWorkerPool.getStats();
  console.log(`\n🔧 Worker Pool Stats:`);
  console.log(`   Total Tasks: ${workerStats.totalTasks}`);
  console.log(`   Completed: ${workerStats.completedTasks}`);
  console.log(`   Average Time: ${workerStats.averageTime.toFixed(2)}ms`);

  // Cache stats
  const cacheStats = fastThumbnailGenerator.getCacheStats();
  console.log(`\n💾 Cache Stats:`);
  console.log(`   Entries: ${cacheStats.entries}`);
  console.log(`   Size: ${cacheStats.totalSizeMB}MB`);

  console.log('\n' + '='.repeat(60));
}

async function runAdvancedPerformanceTest() {
  console.log('🔬 Starting Advanced Performance Test\n');
  
  const testFiles = TEST_IMAGES.length > 0 ? TEST_IMAGES : await createTestImages();
  
  if (testFiles.length === 0) {
    console.log('❌ No test files available for advanced testing.');
    return;
  }

  // Use the performance tester
  const results = await performanceTester.testPerformance(testFiles, 3); // 3 iterations
  performanceTester.printResults(results);

  // Test different sizes
  console.log('\n🔍 Testing Different Thumbnail Sizes...');
  
  const sizes: Array<'small' | 'medium' | 'large'> = ['small', 'medium', 'large'];
  const testFile = testFiles[0];
  
  for (const size of sizes) {
    fastThumbnailGenerator.clearCache();
    const start = Date.now();
    
    try {
      const result = await fastThumbnailGenerator.generateThumbnail(testFile, size);
      const time = Date.now() - start;
      
      // Get file size
      const stats = await fs.stat(result.path);
      console.log(`   ${size}: ${time}ms, ${(stats.size / 1024).toFixed(1)}KB`);
    } catch (error: any) {
      console.log(`   ${size}: Error - ${error.message}`);
    }
  }

  // Test different quality settings
  console.log('\n🎨 Testing Different Quality Settings...');
  
  const qualities = [50, 75, 85, 95];
  
  for (const quality of qualities) {
    fastThumbnailGenerator.clearCache();
    const start = Date.now();
    
    try {
      const result = await fastThumbnailGenerator.generateThumbnail(testFile, 'medium', quality);
      const time = Date.now() - start;
      
      // Get file size
      const stats = await fs.stat(result.path);
      console.log(`   Quality ${quality}: ${time}ms, ${(stats.size / 1024).toFixed(1)}KB`);
    } catch (error: any) {
      console.log(`   Quality ${quality}: Error - ${error.message}`);
    }
  }
}

// Main execution
async function main() {
  console.log('🖼️  ImageScript Thumbnail Performance Test\n');
  
  const args = process.argv.slice(2);
  const testType = args[0] || 'basic';
  
  try {
    if (testType === 'advanced') {
      await runAdvancedPerformanceTest();
    } else {
      await runBasicPerformanceTest();
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    await imageWorkerPool.terminate();
    console.log('\n✅ Test completed');
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { runBasicPerformanceTest, runAdvancedPerformanceTest };
