import fs from "fs/promises";
import { app } from "electron";
import { existsSync, mkdirSync } from "fs";
import path from "path";
import { loadImageScript } from '../main.js';
import { imageWorkerPool } from '../workers/workerPool.js';

// Fast thumbnail generation with optimizations
export class FastThumbnailGenerator {
  private static instance: FastThumbnailGenerator;
  private thumbnailsDir: string;
  private cache = new Map<string, { path: string; base64: string; timestamp: number; size: number }>();
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached thumbnails
  private readonly THUMBNAIL_SIZES = {
    small: 128,
    medium: 256,
    large: 512
  };

  private constructor() {
    this.thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
    if (!existsSync(this.thumbnailsDir)) {
      mkdirSync(this.thumbnailsDir, { recursive: true });
    }

    // Clean up cache periodically
    setInterval(() => this.cleanupCache(), 60 * 1000);
  }

  static getInstance(): FastThumbnailGenerator {
    if (!FastThumbnailGenerator.instance) {
      FastThumbnailGenerator.instance = new FastThumbnailGenerator();
    }
    return FastThumbnailGenerator.instance;
  }

  private cleanupCache(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    // Remove expired entries
    for (const [key, value] of entries) {
      if (now - value.timestamp > this.CACHE_TTL) {
        this.cache.delete(key);
      }
    }

    // If still too many entries, remove oldest ones
    if (this.cache.size > this.MAX_CACHE_SIZE) {
      const sortedEntries = entries
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, this.cache.size - this.MAX_CACHE_SIZE);
      
      for (const [key] of sortedEntries) {
        this.cache.delete(key);
      }
    }
  }

  private getCacheKey(filePath: string, size: number): string {
    return `${filePath}:${size}`;
  }

  private getThumbnailPath(filePath: string, size: number): string {
    const hash = Buffer.from(`${filePath}:${size}`).toString("base64").replace(/[/+=]/g, '_');
    return path.join(this.thumbnailsDir, `${hash}.jpg`);
  }

  async generateThumbnail(
    filePath: string, 
    size: keyof typeof this.THUMBNAIL_SIZES = 'medium',
    quality: number = 85
  ): Promise<{ path: string; base64: string }> {
    const targetSize = this.THUMBNAIL_SIZES[size];
    const cacheKey = this.getCacheKey(filePath, targetSize);
    const thumbnailPath = this.getThumbnailPath(filePath, targetSize);

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && existsSync(cached.path)) {
      return { path: cached.path, base64: cached.base64 };
    }

    // Check if thumbnail file exists
    if (existsSync(thumbnailPath)) {
      const thumbnailBuffer = await fs.readFile(thumbnailPath);
      const base64Data = thumbnailBuffer.toString("base64");
      
      // Update cache
      this.cache.set(cacheKey, {
        path: thumbnailPath,
        base64: base64Data,
        timestamp: Date.now(),
        size: thumbnailBuffer.length
      });

      return { path: thumbnailPath, base64: base64Data };
    }

    // Validate source file exists
    if (!existsSync(filePath)) {
      throw new Error("Source file not found");
    }

    // Generate new thumbnail
    try {
      const workerResult = await imageWorkerPool.executeTask({
        operation: 'generateThumbnail',
        filePath: filePath,
        options: { maxHeight: targetSize, quality }
      });

      if (workerResult.success && workerResult.buffer) {
        const jpegBuffer = Buffer.from(workerResult.buffer);
        await fs.writeFile(thumbnailPath, jpegBuffer);
        
        const base64Data = jpegBuffer.toString("base64");
        
        // Update cache
        this.cache.set(cacheKey, {
          path: thumbnailPath,
          base64: base64Data,
          timestamp: Date.now(),
          size: jpegBuffer.length
        });

        return { path: thumbnailPath, base64: base64Data };
      } else {
        throw new Error(workerResult.error || 'Worker failed to process image');
      }
    } catch (workerError) {
      // Fallback to main thread processing
      console.warn("Worker failed, using main thread:", workerError);
      return await this.generateThumbnailMainThread(filePath, targetSize, quality, thumbnailPath, cacheKey);
    }
  }

  private async generateThumbnailMainThread(
    filePath: string,
    targetSize: number,
    quality: number,
    thumbnailPath: string,
    cacheKey: string
  ): Promise<{ path: string; base64: string }> {
    const ImageScript = await loadImageScript();
    const imageBuffer = await fs.readFile(filePath);
    const image = await ImageScript.decode(imageBuffer);

    // Calculate dimensions
    let newWidth = image.width;
    let newHeight = image.height;

    if (newHeight > targetSize) {
      const aspectRatio = newWidth / newHeight;
      newHeight = targetSize;
      newWidth = Math.round(targetSize * aspectRatio);
    }

    // Resize and encode
    const resizedImage = image.resize(newWidth, newHeight);
    const jpegBuffer = await resizedImage.encodeJPEG(quality);

    // Save to file
    await fs.writeFile(thumbnailPath, jpegBuffer);
    
    const base64Data = jpegBuffer.toString("base64");
    
    // Update cache
    this.cache.set(cacheKey, {
      path: thumbnailPath,
      base64: base64Data,
      timestamp: Date.now(),
      size: jpegBuffer.length
    });

    return { path: thumbnailPath, base64: base64Data };
  }

  // Batch generate thumbnails for multiple files
  async generateBatch(
    filePaths: string[],
    size: keyof typeof this.THUMBNAIL_SIZES = 'medium',
    quality: number = 85
  ): Promise<Array<{ filePath: string; thumbnail: { path: string; base64: string } | null; error?: string }>> {
    const results = await Promise.allSettled(
      filePaths.map(async (filePath) => ({
        filePath,
        thumbnail: await this.generateThumbnail(filePath, size, quality)
      }))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          filePath: filePaths[index],
          thumbnail: null,
          error: result.reason.message
        };
      }
    });
  }

  // Get cache statistics
  getCacheStats() {
    const totalSize = Array.from(this.cache.values()).reduce((sum, item) => sum + item.size, 0);
    return {
      entries: this.cache.size,
      totalSizeBytes: totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2)
    };
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const fastThumbnailGenerator = FastThumbnailGenerator.getInstance();
