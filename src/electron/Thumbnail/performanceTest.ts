import { generateThumbnail } from './generateThumbnail.js';
import { fastThumbnailGenerator } from './fastThumbnailGenerator.js';
import { imageWorkerPool } from '../workers/workerPool.js';
import fs from 'fs/promises';
import path from 'path';

interface PerformanceResult {
  method: string;
  totalTime: number;
  averageTime: number;
  successCount: number;
  errorCount: number;
  errors: string[];
}

export class ThumbnailPerformanceTester {
  async testPerformance(
    testFiles: string[],
    iterations: number = 1
  ): Promise<{
    original: PerformanceResult;
    optimized: PerformanceResult;
    workerStats: any;
  }> {
    console.log(`Starting performance test with ${testFiles.length} files, ${iterations} iterations each`);

    // Test original method
    const originalResult = await this.testMethod(
      'Original Method',
      testFiles,
      iterations,
      (filePath) => generateThumbnail(filePath)
    );

    // Clear cache and reset
    fastThumbnailGenerator.clearCache();
    
    // Test optimized method
    const optimizedResult = await this.testMethod(
      'Optimized Method',
      testFiles,
      iterations,
      (filePath) => fastThumbnailGenerator.generateThumbnail(filePath, 'medium', 85)
    );

    // Get worker pool statistics
    const workerStats = imageWorkerPool.getStats();

    return {
      original: originalResult,
      optimized: optimizedResult,
      workerStats
    };
  }

  private async testMethod(
    methodName: string,
    testFiles: string[],
    iterations: number,
    method: (filePath: string) => Promise<any>
  ): Promise<PerformanceResult> {
    const startTime = Date.now();
    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    console.log(`Testing ${methodName}...`);

    for (let i = 0; i < iterations; i++) {
      for (const filePath of testFiles) {
        try {
          await method(filePath);
          successCount++;
        } catch (error: any) {
          errorCount++;
          errors.push(`${path.basename(filePath)}: ${error.message}`);
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const averageTime = totalTime / (testFiles.length * iterations);

    return {
      method: methodName,
      totalTime,
      averageTime,
      successCount,
      errorCount,
      errors
    };
  }

  async testBatchGeneration(testFiles: string[]): Promise<{
    batchTime: number;
    individualTime: number;
    speedupFactor: number;
  }> {
    console.log('Testing batch generation performance...');

    // Test individual generation
    const individualStart = Date.now();
    for (const filePath of testFiles) {
      try {
        await fastThumbnailGenerator.generateThumbnail(filePath);
      } catch (error) {
        console.warn(`Failed to generate thumbnail for ${filePath}:`, error);
      }
    }
    const individualTime = Date.now() - individualStart;

    // Clear cache for fair comparison
    fastThumbnailGenerator.clearCache();

    // Test batch generation
    const batchStart = Date.now();
    await fastThumbnailGenerator.generateBatch(testFiles);
    const batchTime = Date.now() - batchStart;

    const speedupFactor = individualTime / batchTime;

    return {
      batchTime,
      individualTime,
      speedupFactor
    };
  }

  printResults(results: {
    original: PerformanceResult;
    optimized: PerformanceResult;
    workerStats: any;
  }): void {
    console.log('\n=== THUMBNAIL GENERATION PERFORMANCE RESULTS ===\n');

    console.log('Original Method:');
    console.log(`  Total Time: ${results.original.totalTime}ms`);
    console.log(`  Average Time: ${results.original.averageTime.toFixed(2)}ms per thumbnail`);
    console.log(`  Success Rate: ${results.original.successCount}/${results.original.successCount + results.original.errorCount}`);
    if (results.original.errors.length > 0) {
      console.log(`  Errors: ${results.original.errors.slice(0, 3).join(', ')}${results.original.errors.length > 3 ? '...' : ''}`);
    }

    console.log('\nOptimized Method:');
    console.log(`  Total Time: ${results.optimized.totalTime}ms`);
    console.log(`  Average Time: ${results.optimized.averageTime.toFixed(2)}ms per thumbnail`);
    console.log(`  Success Rate: ${results.optimized.successCount}/${results.optimized.successCount + results.optimized.errorCount}`);
    if (results.optimized.errors.length > 0) {
      console.log(`  Errors: ${results.optimized.errors.slice(0, 3).join(', ')}${results.optimized.errors.length > 3 ? '...' : ''}`);
    }

    const speedup = results.original.averageTime / results.optimized.averageTime;
    const improvement = ((results.original.totalTime - results.optimized.totalTime) / results.original.totalTime) * 100;

    console.log('\nPerformance Improvement:');
    console.log(`  Speedup Factor: ${speedup.toFixed(2)}x`);
    console.log(`  Time Reduction: ${improvement.toFixed(1)}%`);

    console.log('\nWorker Pool Statistics:');
    console.log(`  Total Tasks: ${results.workerStats.totalTasks}`);
    console.log(`  Completed Tasks: ${results.workerStats.completedTasks}`);
    console.log(`  Average Task Time: ${results.workerStats.averageTime.toFixed(2)}ms`);

    console.log('\nCache Statistics:');
    const cacheStats = fastThumbnailGenerator.getCacheStats();
    console.log(`  Cached Entries: ${cacheStats.entries}`);
    console.log(`  Cache Size: ${cacheStats.totalSizeMB}MB`);
  }

  async findTestFiles(directory: string, maxFiles: number = 10): Promise<string[]> {
    const supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    const files: string[] = [];

    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });
      
      for (const entry of entries) {
        if (files.length >= maxFiles) break;
        
        const fullPath = path.join(directory, entry.name);
        
        if (entry.isFile()) {
          const ext = path.extname(entry.name).toLowerCase();
          if (supportedExtensions.includes(ext)) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      console.warn(`Could not read directory ${directory}:`, error);
    }

    return files;
  }
}

// Export singleton instance
export const performanceTester = new ThumbnailPerformanceTester();

// Example usage function
export async function runPerformanceTest(testDirectory?: string): Promise<void> {
  const tester = new ThumbnailPerformanceTester();
  
  // Find test files
  const testFiles = testDirectory 
    ? await tester.findTestFiles(testDirectory, 20)
    : []; // You would need to provide actual test files

  if (testFiles.length === 0) {
    console.log('No test files found. Please provide a directory with image files.');
    return;
  }

  console.log(`Found ${testFiles.length} test files`);

  // Run performance comparison
  const results = await tester.testPerformance(testFiles, 2);
  tester.printResults(results);

  // Test batch generation
  if (testFiles.length > 5) {
    const batchResults = await tester.testBatchGeneration(testFiles.slice(0, 10));
    console.log('\n=== BATCH GENERATION RESULTS ===');
    console.log(`Individual: ${batchResults.individualTime}ms`);
    console.log(`Batch: ${batchResults.batchTime}ms`);
    console.log(`Speedup: ${batchResults.speedupFactor.toFixed(2)}x`);
  }
}
